{"timestamp": "2025-07-02T20:28:05.064740", "test_date": "2025-03-28", "table_tests": {"my.app.tngd.polardb": {"table_name": "my.app.tngd.polardb", "test_date": "2025-03-28", "old_method_results": {"row_count": 0, "query": "from my.app.tngd.polardb where eventdate = '2025-03-28' select * limit 10", "success": true}, "new_method_results": {"row_count": 0, "query": "from my.app.tngd.polardb where eventdate >= 1743091200000 and eventdate <= 1743177599000 select * limit 10", "success": true, "timestamp_range": "1743091200000 to 1743177599000"}, "fix_working": false, "issues": ["New method also returns 0 rows - may indicate no data for this date"]}, "cloud.office365.management.exchange": {"table_name": "cloud.office365.management.exchange", "test_date": "2025-03-28", "old_method_results": {"row_count": 0, "query": "from cloud.office365.management.exchange where eventdate = '2025-03-28' select * limit 10", "success": true}, "new_method_results": {"row_count": 0, "query": "from cloud.office365.management.exchange where eventdate >= 1743091200000 and eventdate <= 1743177599000 select * limit 10", "success": true, "timestamp_range": "1743091200000 to 1743177599000"}, "fix_working": false, "issues": ["New method also returns 0 rows - may indicate no data for this date"]}, "firewall.fortinet.traffic.forward": {"table_name": "firewall.fortinet.traffic.forward", "test_date": "2025-03-28", "old_method_results": {"row_count": 0, "query": "from firewall.fortinet.traffic.forward where eventdate = '2025-03-28' select * limit 10", "success": true}, "new_method_results": {"row_count": 0, "query": "from firewall.fortinet.traffic.forward where eventdate >= 1743091200000 and eventdate <= 1743177599000 select * limit 10", "success": true, "timestamp_range": "1743091200000 to 1743177599000"}, "fix_working": false, "issues": ["New method also returns 0 rows - may indicate no data for this date"]}}, "backup_engine_test": {"test_date": "2025-03-28", "backup_engine_tests": {"my.app.tngd.polardb": {"table_name": "my.app.tngd.polardb", "query_constructed": "eventdate >= 1743091200000 and eventdate <= 1743177599000", "data_retrieved": false, "row_count": 0, "issues": ["No data retrieved with new filtering logic"]}}, "integration_working": false, "issues": ["No successful integration tests"]}, "summary": {"total_tables_tested": 3, "tables_with_working_fix": 0, "backup_engine_working": false, "overall_fix_status": "FAILED", "issues_found": ["New method also returns 0 rows - may indicate no data for this date", "New method also returns 0 rows - may indicate no data for this date", "New method also returns 0 rows - may indicate no data for this date", "No successful integration tests"]}}