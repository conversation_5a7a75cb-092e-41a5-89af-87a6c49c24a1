#!/usr/bin/env python3
"""
Test what data is actually available in the Devo system.
"""

import os
import sys
import json
import logging
from datetime import datetime, timedelta
from pathlib import Path

# Add src to Python path
project_root = Path(__file__).parent
sys.path.insert(0, str(project_root / "src"))

try:
    from tngd_backup.core.devo_client import DevoClient
    from tngd_backup.core.config_manager import ConfigManager
except ImportError as e:
    print(f"ERROR: Failed to import required modules: {e}")
    sys.exit(1)


def test_data_availability():
    """Test what data is actually available."""
    
    # Setup logging
    logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
    logger = logging.getLogger(__name__)
    
    # Initialize
    config_manager = ConfigManager()
    devo_client = DevoClient(config_manager)
    
    test_tables = [
        'my.app.tngd.polardb',
        'cloud.office365.management.exchange',
        'firewall.fortinet.traffic.forward'
    ]
    
    print("=== TESTING DATA AVAILABILITY ===\n")
    
    for table_name in test_tables:
        print(f"📊 Testing table: {table_name}")
        
        try:
            # Test 1: Get any recent data (last 7 days)
            query1 = f"from {table_name} select * limit 5"
            results1 = devo_client.execute_query(query1, days=7, timeout=300)
            print(f"  Recent data (7 days): {len(results1)} rows")
            
            if results1:
                sample_row = results1[0]
                print(f"  Sample fields: {list(sample_row.keys())[:10]}")
                
                if 'eventdate' in sample_row:
                    eventdate_value = sample_row['eventdate']
                    print(f"  Sample eventdate: {eventdate_value} (type: {type(eventdate_value).__name__})")
                    
                    # If it's a timestamp, convert it
                    if isinstance(eventdate_value, (int, float)):
                        try:
                            if eventdate_value > 1000000000000:  # milliseconds
                                dt = datetime.fromtimestamp(eventdate_value / 1000)
                            else:  # seconds
                                dt = datetime.fromtimestamp(eventdate_value)
                            print(f"  Converted date: {dt.strftime('%Y-%m-%d %H:%M:%S')}")
                        except:
                            print(f"  Could not convert timestamp")
                else:
                    print(f"  ❌ No 'eventdate' field found!")
                    # Look for other date fields
                    date_fields = [k for k in sample_row.keys() if 'date' in k.lower() or 'time' in k.lower()]
                    if date_fields:
                        print(f"  Other date fields: {date_fields}")
                        for field in date_fields[:3]:  # Show first 3
                            print(f"    {field}: {sample_row[field]}")
            else:
                print(f"  ❌ No data found in last 7 days")
            
            # Test 2: Try to get data for March 2025 (broader range)
            query2 = f"from {table_name} where eventdate >= 1740700800000 and eventdate <= 1743292799999 select * limit 5"
            results2 = devo_client.execute_query(query2, days=30, timeout=300)
            print(f"  March 2025 data: {len(results2)} rows")
            
            # Test 3: Try without any date filter (last 30 days)
            query3 = f"from {table_name} select * limit 10"
            results3 = devo_client.execute_query(query3, days=30, timeout=300)
            print(f"  Any data (30 days): {len(results3)} rows")
            
        except Exception as e:
            print(f"  ❌ Error testing {table_name}: {e}")
        
        print()
    
    print("=== SUMMARY ===")
    print("If all tables show 0 rows, the issue might be:")
    print("1. No data exists for the tested time periods")
    print("2. Different date field name (not 'eventdate')")
    print("3. Different date format or timezone")
    print("4. Access permissions or table names changed")


if __name__ == "__main__":
    test_data_availability()
