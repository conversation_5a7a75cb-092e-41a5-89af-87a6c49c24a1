{"version": "2.0", "description": "TNGD Backup System - Default Configuration", "resource_management": {"max_threads": 4, "memory_threshold_mb": 1500, "cpu_threshold_percent": 75, "disk_threshold_percent": 85, "cleanup_interval_seconds": 180, "resource_check_interval_seconds": 30}, "query_settings": {"default_timeout_seconds": 1200, "large_table_timeout_seconds": 2400, "max_retries": 2, "retry_delay_seconds": 30, "connection_timeout_seconds": 20, "read_timeout_seconds": 180}, "streaming_config": {"enabled": true, "default_chunk_size": 20000, "max_chunk_size": 50000, "min_chunk_size": 5000, "streaming_threshold_rows": 50000, "memory_threshold_mb": 1000, "progress_report_interval": 3, "memory_check_interval": 2, "enable_adaptive_chunking": true, "chunk_size_adjustment_factor": 0.7, "temp_file_cleanup": true}, "storage_settings": {"upload_timeout_seconds": 3600, "max_upload_retries": 3, "retry_delay_seconds": 30, "chunk_size_mb": 50, "memory_threshold_percent": 75, "connection_pool_size": 3, "verify_integrity": true, "compress_before_upload": true, "upload_path_structure": {"base_path": "", "provider_path": "Devo", "use_month_folders": true, "use_week_folders": true, "use_date_folders": true, "include_table_folders": false}}, "monitoring": {"enabled": true, "log_level": "INFO", "metrics_collection": true, "health_check_interval_seconds": 300, "alert_thresholds": {"cpu_warning": 80, "cpu_critical": 95, "memory_warning": 75, "memory_critical": 90, "thread_warning": 100, "thread_critical": 200}}, "large_tables": ["cef0.zscaler.nssweblog", "cloud.alibaba.log_service.events", "cloud.office365.management.exchange", "firewall.fortinet.traffic.forward", "cloud.office365.management.endpoint", "my.app.tngd.polardb", "netstat.zscaler.analyzer_zpa"], "table_specific_settings": {"cef0.zscaler.nssweblog": {"chunk_size": 10000, "timeout_seconds": 3600, "max_retries": 3, "memory_limit_mb": 500}, "cloud.alibaba.log_service.events": {"chunk_size": 15000, "timeout_seconds": 3600, "max_retries": 3, "memory_limit_mb": 600}, "firewall.fortinet.traffic.forward": {"chunk_size": 15000, "timeout_seconds": 2400, "max_retries": 3, "memory_limit_mb": 600}, "cloud.office365.management.endpoint": {"chunk_size": 15000, "timeout_seconds": 2400, "max_retries": 3, "memory_limit_mb": 600}}, "error_handling": {"max_consecutive_failures": 3, "failure_cooldown_minutes": 5, "auto_skip_problematic_tables": true, "detailed_error_logging": true, "error_notification_threshold": 3}, "recovery": {"checkpoint_enabled": true, "checkpoint_interval_minutes": 15, "auto_resume": true, "max_resume_attempts": 3, "resume_delay_minutes": 5}, "performance_optimizations": {"connection_pooling": true, "query_caching": false, "parallel_uploads": true, "memory_mapping": false, "compression_level": 6, "buffer_size_kb": 64}, "logging": {"level": "INFO", "max_file_size_mb": 100, "backup_count": 5, "format": "%(asctime)s - %(name)s - %(levelname)s - %(message)s", "separate_error_log": true, "log_rotation": true}}