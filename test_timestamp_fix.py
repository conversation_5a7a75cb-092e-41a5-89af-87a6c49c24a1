#!/usr/bin/env python3
"""
Test the corrected timestamp calculation to ensure it's working properly.
"""

from datetime import datetime

def test_timestamp_calculation(date_str):
    """Test the corrected timestamp calculation."""
    print(f"\n=== Testing timestamp calculation for {date_str} ===")
    
    # Parse the date string
    target_date = datetime.strptime(date_str, '%Y-%m-%d')
    print(f"Target date: {target_date}")
    
    # CORRECTED TIMESTAMP CALCULATION: Use exact day boundaries
    # Start of day (00:00:00)
    start_of_day = target_date.replace(hour=0, minute=0, second=0, microsecond=0)
    # End of day (23:59:59.999)
    end_of_day = target_date.replace(hour=23, minute=59, second=59, microsecond=999000)
    
    print(f"Start of day: {start_of_day}")
    print(f"End of day: {end_of_day}")
    
    # Convert to milliseconds timestamp
    start_timestamp = int(start_of_day.timestamp() * 1000)
    end_timestamp = int(end_of_day.timestamp() * 1000)
    
    print(f"Start timestamp: {start_timestamp}")
    print(f"End timestamp: {end_timestamp}")
    
    # Verify by converting back
    start_verify = datetime.fromtimestamp(start_timestamp / 1000)
    end_verify = datetime.fromtimestamp(end_timestamp / 1000)
    
    print(f"Start timestamp converts back to: {start_verify}")
    print(f"End timestamp converts back to: {end_verify}")
    
    # Check if they match the expected date
    start_date_str = start_verify.strftime('%Y-%m-%d')
    end_date_str = end_verify.strftime('%Y-%m-%d')
    
    print(f"Start date string: {start_date_str}")
    print(f"End date string: {end_date_str}")
    
    if start_date_str == date_str and end_date_str == date_str:
        print("✅ TIMESTAMP CALCULATION CORRECT!")
        return True
    else:
        print("❌ TIMESTAMP CALCULATION INCORRECT!")
        return False

def main():
    """Test multiple dates."""
    test_dates = ['2025-03-28', '2025-03-29', '2025-03-30', '2025-03-31']
    
    all_correct = True
    for date_str in test_dates:
        correct = test_timestamp_calculation(date_str)
        if not correct:
            all_correct = False
    
    print(f"\n{'='*50}")
    if all_correct:
        print("✅ ALL TIMESTAMP CALCULATIONS ARE CORRECT!")
    else:
        print("❌ SOME TIMESTAMP CALCULATIONS ARE INCORRECT!")
    print(f"{'='*50}")

if __name__ == "__main__":
    main()
