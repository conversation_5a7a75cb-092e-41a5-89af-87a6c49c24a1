#!/usr/bin/env python3
"""
Find what dates actually have data in the Devo system.
"""

import os
import sys
import logging
from datetime import datetime
from pathlib import Path

# Add src to Python path
project_root = Path(__file__).parent
sys.path.insert(0, str(project_root / "src"))

try:
    from tngd_backup.core.devo_client import DevoClient
    from tngd_backup.core.config_manager import ConfigManager
except ImportError as e:
    print(f"ERROR: Failed to import required modules: {e}")
    sys.exit(1)


def find_available_dates():
    """Find what dates have data."""
    
    # Setup logging
    logging.basicConfig(level=logging.WARNING)  # Reduce noise
    
    # Initialize
    config_manager = ConfigManager()
    devo_client = DevoClient(config_manager)
    
    test_tables = [
        'my.app.tngd.polardb',
        'cloud.office365.management.exchange', 
        'firewall.fortinet.traffic.forward'
    ]
    
    print("🔍 FINDING AVAILABLE DATA DATES\n")
    
    all_dates = set()
    
    for table_name in test_tables:
        print(f"📊 Checking {table_name}...")
        
        try:
            # Get sample of recent data
            query = f"from {table_name} select eventdate limit 20"
            results = devo_client.execute_query(query, days=30, timeout=300)
            
            if results:
                dates_found = set()
                for row in results:
                    if 'eventdate' in row:
                        eventdate = row['eventdate']
                        if isinstance(eventdate, (int, float)):
                            try:
                                # Convert timestamp to date
                                if eventdate > 1000000000000:  # milliseconds
                                    dt = datetime.fromtimestamp(eventdate / 1000)
                                else:  # seconds
                                    dt = datetime.fromtimestamp(eventdate)
                                date_str = dt.strftime('%Y-%m-%d')
                                dates_found.add(date_str)
                                all_dates.add(date_str)
                            except:
                                pass
                
                if dates_found:
                    print(f"  ✅ Found data for dates: {sorted(dates_found)}")
                else:
                    print(f"  ⚠️ Found {len(results)} rows but no valid eventdate timestamps")
            else:
                print(f"  ❌ No data found")
                
        except Exception as e:
            print(f"  ❌ Error: {e}")
    
    print(f"\n{'='*60}")
    print("📅 SUMMARY OF AVAILABLE DATES:")
    print(f"{'='*60}")
    
    if all_dates:
        sorted_dates = sorted(all_dates)
        print(f"✅ Data available for {len(sorted_dates)} unique dates:")
        for date in sorted_dates:
            print(f"  📅 {date}")
        
        print(f"\n🚀 RECOMMENDED BACKUP COMMANDS:")
        print(f"{'='*40}")
        
        # Single date backup
        latest_date = sorted_dates[-1]
        print(f"# Backup latest date:")
        print(f"python run_backup.py {latest_date}")
        
        # Range backup if multiple dates
        if len(sorted_dates) > 1:
            earliest_date = sorted_dates[0]
            print(f"\n# Backup all available dates:")
            print(f"python run_backup.py {earliest_date} {latest_date}")
        
    else:
        print("❌ No valid dates found in any table!")
        print("\nPossible issues:")
        print("- Data uses different date field (not 'eventdate')")
        print("- Data uses different timestamp format")
        print("- No recent data in the system")
    
    print(f"\n{'='*60}")


if __name__ == "__main__":
    find_available_dates()
